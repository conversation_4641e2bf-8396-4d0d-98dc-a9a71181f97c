#!/bin/bash

# 项目配置
project_name=verl_grpo_mmk12
experiment_name=qwen2_5_vl_7b_mmk12_first_epoch_72b_rollout

# 节点和GPU配置
nnodes=1
n_gpus_per_node=8

# 数据配置
train_files=/share/huangzihan/data/mmk12/train_72b_rollout.parquet
val_files=/share/huangzihan/data/mmk12/test.parquet
train_batch_size=512
max_prompt_length=10240
max_response_length=16384
filter_overlong_prompts=false
truncation=error

# 算法配置
adv_estimator=grpo
use_kl_in_reward=false

# 奖励模型配置
reward_manager=naive

# 模型配置
model_path=/share/tianyang/huggingface_model/Qwen/Qwen2.5-VL-7B-Instruct
use_liger=true
use_fused_kernels=false  # 这一项不要动，永远设置为false

# 学习率配置
lr=1e-6
weight_decay=0.01
lr_warmup_steps=-1
lr_warmup_steps_ratio=0.0
min_lr_ratio=0.0
num_cycles=0.5
warmup_style=constant

# PPO配置
ppo_mini_batch_size=128
ppo_micro_batch_size_per_gpu=1
clip_ratio_low=0.2
clip_ratio_high=0.2
clip_ratio_c=3.0
use_kl_loss=true
kl_loss_coef=0.01
kl_loss_type=low_var_kl
entropy_coeff=0
ulysses_sequence_parallel_size=1
fsdp_size=$n_gpus_per_node
actor_offload=false

# Rollout配置
rollout_name=vllm
log_prob_micro_batch_size_per_gpu=10
tensor_model_parallel_size=1
gpu_memory_utilization=0.9
enable_chunked_prefill=false
enforce_eager=false
free_cache_engine=true
n=5

# Reference模型配置
ref_offload=true

# 训练配置
save_freq=20
test_freq=5
total_epochs=10
default_local_dir=/share/huangzihan/rl/baseline/$project_name/$experiment_name
rollout_data_dir=/share/huangzihan/rl/baseline/$project_name/$experiment_name/rollout
validation_data_dir=/share/huangzihan/rl/baseline/$project_name/$experiment_name/validation

# 奖励函数配置
reward_fn_name=reward_fn
reward_fn_file_path=/share/huangzihan/data/mmk12/reward.py
